/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: "export", // Ensures static export
  // basePath: "/kapoorsoftwaresolutions", // Use your repository name
  images: {
    // unoptimized: true, // Required for GitHub Pages
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'spaces.kapoorsoftware.com',
        port: '',
        pathname: '/kapoorsoftware/media/**',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/mobile-app-development',
        destination: '/services/enterprise-mobile-app-development',
        permanent: true, // 301 redirect for SEO
      },
      {
        source: '/AI',
        destination: '/services/ai-business-solutions',
        permanent: true,
      },
      {
        source: '/App_Integration',
        destination: '/services/enterprise-application-integration-services',
        permanent: true,
      },
      {
        source: '/Business_Intelligence',
        destination: '/services/business-intelligence-analytics-services',
        permanent: true,
      },
      {
        source: '/Cloud_Services',
        destination: '/services/managed-cloud-services',
        permanent: true,
      },
      {
        source: '/custom-software-development',
        destination: '/services/enterprise-software-development',
        permanent: true,
      },
      {
        source: '/custom-website-development',
        destination: '/services/custom-web-development-services',
        permanent: true,
      },
      {
        source: '/Data_and_Analytics',
        destination: '/services/data-analytics-services',
        permanent: true,
      },
      {
        source: '/DataEngineering',
        destination: '/services/data-engineering-solutions',
        permanent: true,
      },
      {
        source: '/Dedicated_Deployment_teams',
        destination: '/services/dedicated-development-team',
        permanent: true,
      },
      {
        source: '/financial-app-development',
        destination: '/services/fintech-software-development',
        permanent: true,
      },
      {
        source: '/Full_Stack_Development_Services',
        destination: '/services/full-stack-development-services',
        permanent: true,
      },
      {
        source: '/health-care-development',
        destination: '/services/healthcare-software-development',
        permanent: true,
      },
      {
        source: '/Maintenance_and_Support',
        destination: '/services/it-maintenance-support-services',
        permanent: true,
      },
      {
        source: '/ML',
        destination: '/services/machine-learning-services',
        permanent: true,
      },
      {
        source: '/product-management',
        destination: '/services/product-management-consulting',
        permanent: true,
      },
      {
        source: '/Quality_Assurance',
        destination: '/services/quality-assurance-services',
        permanent: true,
      },
      {
        source: '/saas-app-development',
        destination: '/services/saas-application-development',
        permanent: true,
      },
      {
        source: '/ui-ux',
        destination: '/services/ui-ux-design-services',
        permanent: true,
      },
      {
        source: '/Industries/Healthcare',
        destination: '/industries/healthcare-it-solutions',
        permanent: true,
      },
      {
        source: '/Industries/Construction',
        destination: '/industries/construction-software-development',
        permanent: true,
      },
      {
        source: '/Industries/Telecom',
        destination: '/industries/telecom-software-development',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
