// Helper function to validate and optimize image URLs for social sharing
function validateAndOptimizeImage(imageUrl, fallbackUrl = "https://valueans.com/Images/default-blog-og.png") {
  // Basic URL validation
  if (!imageUrl || typeof imageUrl !== 'string') {
    return fallbackUrl;
  }

  // Ensure the image URL is absolute
  if (imageUrl.startsWith('//')) {
    return `https:${imageUrl}`;
  } else if (imageUrl.startsWith('/')) {
    return `https://valueans.com${imageUrl}`;
  } else if (!imageUrl.startsWith('http')) {
    return fallbackUrl;
  }

  return imageUrl;
}

async function fetchBlogData(slug) {
  try {
    const response = await fetch("https://api.valueans.com/api/blogs/", {
      // Add cache revalidation for better performance
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Find the blog with matching slug
    const foundBlog = data.results?.find(blog => blog.slug_field === slug);

    return foundBlog;
  } catch (error) {
    console.error("Error fetching blog data for metadata:", error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const blog = await fetchBlogData(params.slug);

  // Fallback metadata if blog is not found
  if (!blog) {
    const fallbackTitle = `Blog Post - ${params.slug} | Valueans`;
    const fallbackDescription = "Read our latest blog post about software development, technology trends, and business insights.";
    const fallbackUrl = `https://valueans.com/blog/${params.slug}`;

    return {
      title: fallbackTitle,
      description: fallbackDescription,
      alternates: {
        canonical: fallbackUrl,
      },
      openGraph: {
        title: fallbackTitle,
        description: fallbackDescription,
        url: fallbackUrl,
        type: "article",
        siteName: "Valueans",
        locale: "en_US",
        images: [
          {
            url: "https://valueans.com/Images/default-blog-og.png",
            width: 1200,
            height: 627, // Updated for better LinkedIn compatibility
            alt: "Valueans Blog",
            type: "image/png",
          },
          {
            url: "https://valueans.com/Images/default-blog-og.png",
            width: 1200,
            height: 675, // Twitter-optimized fallback
            alt: "Valueans Blog",
            type: "image/png",
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: fallbackTitle,
        description: fallbackDescription,
        images: ["https://valueans.com/Images/default-blog-og.png"], // Using default image
        site: "@valueans",
      },
    };
  }

  // Dynamic metadata based on blog data
  const title = `${blog.title}`;

  // Generate descriptions optimized for different platforms
  let metaDescription; // For standard meta description (160 chars max)
  let socialDescription; // For Open Graph/social media (300 chars max)

  if (blog.description_for_seo) {
    // Use the full description_for_seo for social media (no truncation)
    socialDescription = blog.description_for_seo.trim();
    // Truncate only for meta description if needed
    metaDescription = socialDescription.length > 160
      ? socialDescription.substring(0, 160) + '...'
      : socialDescription;
  } else if (blog.content) {
    // Strip HTML and create descriptions from content
    const strippedContent = blog.content.replace(/<[^>]*>/g, '').trim();
    // For social media: allow up to 300 characters
    socialDescription = strippedContent.length > 300
      ? strippedContent.substring(0, 300) + '...'
      : strippedContent;
    // For meta description: limit to 160 characters
    metaDescription = strippedContent.length > 160
      ? strippedContent.substring(0, 160) + '...'
      : strippedContent;
  } else {
    // Generic fallback for both
    const fallbackText = "Read our latest blog post about software development, technology trends, and business insights.";
    socialDescription = fallbackText;
    metaDescription = fallbackText;
  }

  const canonicalUrl = `https://valueans.com/blog/${params.slug}`;

  // Optimize image for social media sharing - prevent cropping across platforms
  const ogImage = validateAndOptimizeImage(blog.image, "https://valueans.com/Images/default-blog-og.png");

  // Create multiple image sizes for optimal display across platforms
  const socialImages = [
    {
      // Primary image - optimized for Facebook, LinkedIn, and general sharing
      // Using 1200x627 (1.91:1) - LinkedIn's preferred ratio, works well on Facebook
      url: ogImage,
      width: 1200,
      height: 627, // Changed from 630 to 627 for better LinkedIn compatibility
      alt: blog.title,
      type: "image/jpeg",
    },
    {
      // Secondary image - optimized for Twitter
      // Using 1200x675 for better Twitter display without cropping
      url: ogImage,
      width: 1200,
      height: 675,
      alt: blog.title,
      type: "image/jpeg",
    },
    {
      // Fallback square image for platforms that prefer 1:1 ratio
      url: ogImage,
      width: 1200,
      height: 1200,
      alt: blog.title,
      type: "image/jpeg",
    }
  ];

  return {
    title,
    description: metaDescription, // Use truncated description for meta tag
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description: socialDescription, // Use full description for social sharing
      url: canonicalUrl,
      type: "article",
      siteName: "Valueans", // Required for LinkedIn
      locale: "en_US", // Required for proper LinkedIn parsing
      images: socialImages, // Use optimized image array for better cross-platform compatibility
      publishedTime: blog.created_at,
      modifiedTime: blog.updated_at || blog.created_at, // LinkedIn prefers this
      authors: [blog.uploaded_by?.name || 'Valueans'],
      section: blog.categories?.name || 'Technology', // Article section for LinkedIn
    },
    twitter: {
      card: "summary_large_image",
      title,
      description: socialDescription, // Use full description for Twitter
      images: [socialImages[1].url], // Use Twitter-optimized image (1200x675)
      site: "@valueans", // Add Twitter handle if available
    },
    // Additional meta tags for better social media compatibility
    other: {
      // LinkedIn specific meta tags
      'article:author': blog.uploaded_by?.name || 'Valueans',
      'article:published_time': blog.created_at,
      'article:modified_time': blog.updated_at || blog.created_at,
      'article:section': blog.categories?.name || 'Technology',
      // Ensure proper encoding
      'charset': 'utf-8',
    },
  };
}

export default function BlogSlugLayout({ children }) {
  return children;
}
